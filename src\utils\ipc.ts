import { ElMessage } from 'element-plus'

/**
 * IPC 通信结果类型
 */
export interface IpcResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

/**
 * IPC 配置选项
 */
export interface IpcOptions {
  showSuccessMessage?: boolean
  showErrorMessage?: boolean
  successMessage?: string
  errorMessage?: string
  silent?: boolean
}

/**
 * 统一的 IPC 管理类
 */
class IpcManager {
  private ipcRenderer: any = null

  /**
   * 获取 ipcRenderer 实例
   */
  private getIpcRenderer() {
    if (!this.ipcRenderer) {
      try {
        this.ipcRenderer = window.require('electron').ipcRenderer
      } catch (error) {
        console.error('Failed to get ipcRenderer:', error)
        throw new Error('无法获取 Electron IPC 通信接口')
      }
    }
    return this.ipcRenderer
  }

  /**
   * 显示成功消息
   */
  private showSuccessMessage(message: string) {
    ElMessage({
      message,
      type: 'success',
      duration: 1000
    })
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(message: string) {
    ElMessage.error(message)
  }

  /**
   * 通用 IPC 调用方法
   */
  async invoke<T = any>(
    channel: string,
    data?: any,
    options: IpcOptions = {}
  ): Promise<IpcResult<T>> {
    const {
      showSuccessMessage = false,
      showErrorMessage = true,
      successMessage = '操作成功',
      errorMessage = '操作失败',
      silent = false
    } = options

    try {
      const ipcRenderer = this.getIpcRenderer()
      const result = await ipcRenderer.invoke(channel, data)

      // 如果结果是 false，视为失败
      if (result === false) {
        const error = errorMessage
        if (showErrorMessage && !silent) {
          this.showErrorMessage(error)
        }
        return {
          success: false,
          error
        }
      }

      // 成功情况
      if (showSuccessMessage && !silent) {
        this.showSuccessMessage(successMessage)
      }

      return {
        success: true,
        data: result
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error)
      console.error(`IPC call failed [${channel}]:`, error)

      if (showErrorMessage && !silent) {
        this.showErrorMessage(errorMessage)
      }

      return {
        success: false,
        error: errorMsg
      }
    }
  }

  /**
   * 获取配置
   */
  async getConfig<T = any>(options: IpcOptions = {}): Promise<IpcResult<T>> {
    return this.invoke<T>('get-config', undefined, {
      showErrorMessage: true,
      errorMessage: '加载配置失败',
      ...options
    })
  }

  /**
   * 保存配置
   */
  async saveConfig<T = any>(
    config: T,
    options: IpcOptions = {}
  ): Promise<IpcResult<boolean>> {
    return this.invoke<boolean>('save-config', config, {
      showSuccessMessage: true,
      showErrorMessage: true,
      successMessage: '设置已保存',
      errorMessage: '保存失败',
      ...options
    })
  }
}

// 创建单例实例
const ipcManager = new IpcManager()

// 导出实例和类型
export { ipcManager }
export default ipcManager

/**
 * 便捷的 IPC 调用函数
 */
export const ipcInvoke = ipcManager.invoke.bind(ipcManager)
export const getConfig = ipcManager.getConfig.bind(ipcManager)
export const saveConfig = ipcManager.saveConfig.bind(ipcManager)
